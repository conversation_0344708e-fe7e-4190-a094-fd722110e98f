# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
node_modules/
.pnp
.pnp.js
**/node_modules/

# Local env files
.env
.env.*
!.env.example

# Testing
coverage/
.nyc_output/

# Turborepo
.turbo/

# Build Outputs
dist/
build/
.next/
out/

# Remix
.cache/
build/
public/build/

# TypeScript
*.tsbuildinfo
.tscache/

# Cloudflare
wrangler.toml.backup
.wrangler/
.dev.vars

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Temporary folders
tmp/
temp/

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Package manager lockfiles (keep pnpm-lock.yaml)
package-lock.json
yarn.lock

# SSL certificates
*.pem
*.key
*.crt

# Database
*.db
*.sqlite
*.sqlite3

# Biome (no specific cache to ignore for biome)

# Misc
*.tgz
*.tar.gz
