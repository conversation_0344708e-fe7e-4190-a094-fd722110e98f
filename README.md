# Remix Cloudflare Workers Neon Starter

A modern, production-ready SaaS starter template built with Remix running entirely on Cloudflare Workers and Neon PostgreSQL. This comprehensive monorepo provides everything you need to build and deploy a scalable SaaS application using a single Worker architecture.

## ✨ What's Included

- 🚀 **Remix** application deployed as a single Cloudflare Worker
- 🔐 **BetterAuth** authentication system with multiple providers
- 🗄️ **Neon PostgreSQL** database with Drizzle ORM
- 🎨 **Radix UI** component library with Tailwind CSS
- 📧 **Email** functionality with mailer integration
- 💳 **Stripe** billing integration (in development)
- 📊 **Analytics** and monitoring setup
- 🤖 **AI** integration capabilities
- 📱 **Responsive** design with dark/light theme support
- ⚡ **Edge Runtime** with global deployment on Cloudflare's network

## 🏗️ Architecture

This starter uses a unified single-Worker architecture optimized for Cloudflare's edge network:

```
├── apps/
│   └── web/                # Single Remix Worker Application
│       ├── app/            # Remix application code
│       │   ├── routes/     # File-based routing + API routes
│       │   ├── lib/        # Shared utilities and services
│       │   └── components/ # React components
│       ├── build/          # Build output
│       │   ├── client/     # Static assets served by Worker
│       │   └── server/     # SSR bundle
│       ├── worker.js       # Cloudflare Worker entry point
│       └── wrangler.toml   # Worker configuration
└── packages/               # Shared packages
    ├── auth/               # BetterAuth authentication system
    ├── db/                 # Database schemas and migrations
    ├── ui-kit/             # React component library
    ├── shared/             # Common utilities and types
    ├── config/             # Environment configuration
    ├── billing/            # Stripe payment integration
    ├── storage/            # Cloudflare R2 file storage
    ├── mailer/             # Email functionality
    ├── monitoring/         # Logging and error tracking
    └── [others]/           # Additional feature packages
```

### Single Worker Benefits
- 🌍 **Global Edge Deployment**: Deploy to 300+ locations worldwide
- ⚡ **Zero Cold Start**: Instant response times with V8 isolates
- 💰 **Cost Effective**: Pay only for actual usage, no idle costs
- 🔧 **Simplified Architecture**: No separate API server needed
- 🚀 **Automatic Scaling**: Handle millions of requests seamlessly

## 🚀 Tech Stack

### Core Technologies
- **Frontend**: [Remix](https://remix.run/) + [React 19](https://reactjs.org/)
- **Runtime**: [Cloudflare Workers](https://workers.cloudflare.com/) (Single Worker Architecture)
- **Database**: [Neon PostgreSQL](https://neon.tech/) + [Drizzle ORM](https://orm.drizzle.team/)
- **Authentication**: [BetterAuth](https://better-auth.com/)
- **Storage**: [Cloudflare R2](https://developers.cloudflare.com/r2/)
- **Payments**: [Stripe](https://stripe.com/)

### Development Experience
- **Language**: [TypeScript 5.8](https://www.typescriptlang.org/)
- **Build Tool**: [Turbo](https://turbo.build/)
- **Package Manager**: [pnpm](https://pnpm.io/)
- **Styling**: [Tailwind CSS](https://tailwindcss.com/)
- **Components**: [Radix UI](https://www.radix-ui.com/)
- **Code Quality**: [Biome](https://biomejs.dev/) (formatting & linting)

### Cloudflare Platform Features
- **AI**: [Cloudflare AI](https://developers.cloudflare.com/ai/) for machine learning
- **KV**: [Cloudflare KV](https://developers.cloudflare.com/kv/) for caching
- **D1**: [Cloudflare D1](https://developers.cloudflare.com/d1/) for edge SQL (optional)
- **Analytics**: [Cloudflare Analytics](https://developers.cloudflare.com/analytics/) for monitoring

## 📦 Package Overview

### Core Packages (Active Development)

#### `@repo/auth` - Authentication System
- BetterAuth integration with multiple providers
- Session management and user context
- Role-based access control ready

#### `@repo/db` - Database Layer  
- Drizzle ORM with Neon PostgreSQL
- Type-safe database operations
- Migration system included

#### `@repo/ui-kit` - Component Library
- 20+ React components built with Radix UI
- Tailwind CSS styling with CSS variables
- Dark/light theme support
- TypeScript definitions included

#### `@repo/shared` - Common Utilities
- TypeScript types and interfaces
- Validation schemas with Zod
- Utility functions and constants

#### `@repo/config` - Configuration Management
- Environment variable management
- Type-safe configuration loading

### Feature Packages (Ready for Development)

#### `@repo/billing` - Payment Integration
- Stripe integration setup
- Subscription management (in progress)
- Webhook handling

#### `@repo/storage` - File Management
- Cloudflare R2 and S3 providers
- File upload utilities

#### `@repo/mailer` - Email System
- Email sending capabilities
- Template management ready

#### `@repo/monitoring` - Observability
- Error tracking and logging
- Performance monitoring setup

### Planned Packages (Roadmap)
- `@repo/ai` - AI/ML integration
- `@repo/analytics` - User analytics
- `@repo/cms` - Content management
- `@repo/i18n` - Internationalization
- `@repo/email-templates` - Email templates

## 🚀 Quick Start

### Prerequisites
- [Node.js](https://nodejs.org/) 18+ 
- [pnpm](https://pnpm.io/) (recommended package manager)
- [Cloudflare account](https://cloudflare.com/) for deployment
- [Neon PostgreSQL](https://neon.tech/) database

### Installation

1. **Clone and install dependencies:**
   ```bash
   git clone <your-repo>
   cd remix-cloudflare-neon-starter
   pnpm install
   ```

2. **Set up environment variables:**
   ```bash
   cp apps/web/.env.example apps/web/.env.local
   # Configure your database, auth, and Cloudflare settings
   ```

3. **Initialize the database:**
   ```bash
   pnpm db:push
   ```

4. **Start development:**
   ```bash
   pnpm dev
   ```

Visit http://localhost:5133 for the development application.

## 🛠️ Development Commands

### Global Commands
```bash
pnpm dev              # Start all applications
pnpm build            # Build all apps and packages  
pnpm lint             # Run Biome linting
pnpm format           # Format code with Biome
pnpm check-types      # TypeScript type checking
pnpm db:push          # Push database schema changes
pnpm db:studio        # Open Drizzle Studio
```

### Filtered Development
```bash
# Start only the main application
pnpm turbo dev --filter=web     # Single Worker app

# Build specific packages
pnpm turbo build --filter=ui-kit
pnpm turbo build --filter=auth
```

### Component Development
```bash
# Generate new UI components
cd packages/ui-kit
pnpm generate:component
```

## 🏗️ Project Structure Details

### Single Worker Application
- **`apps/web/`** - Complete Remix SaaS application running as a single Worker
  - **Routing**: File-based routing with API routes (`/api/*`)
  - **SSR**: Server-side rendering with streaming
  - **Static Assets**: Served directly from the Worker
  - **Authentication**: Integrated BetterAuth system
  - **Database**: Direct Neon PostgreSQL connections
  - **Storage**: Cloudflare R2 integration
  - **AI**: Cloudflare AI model access

### Key Package Exports
- **`@repo/shared`**: `./types`, `./constants`, `./utils`
- **`@repo/ui-kit`**: `.`, `./components/*`, `./primitives/*`, `./styles/*`
- **`@repo/auth`**: `.`, `./config`, `./providers`
- **`@repo/db`**: `.`, `./schema`, `./migrations`

## 🚀 Deployment

### Cloudflare Workers Deployment

1. **Configure Cloudflare:**
   ```bash
   # Install Wrangler CLI
   npm install -g wrangler
   
   # Login to Cloudflare
   wrangler login
   ```

2. **Deploy the application:**
   ```bash
   # Build and deploy in one command
   pnpm deploy
   
   # Or manually step by step
   pnpm build
   cd apps/web
   pnpm deploy
   ```

3. **Configure environment variables:**
   ```bash
   # Set secrets in Cloudflare
   wrangler secret put DATABASE_URL
   wrangler secret put AUTH_SECRET
   wrangler secret put STRIPE_SECRET_KEY
   ```

4. **Custom Domain (Optional):**
   ```bash
   # Add custom domain in Cloudflare Dashboard
   # Or configure in wrangler.toml
   ```

### Deployment Features
- 🌍 **Global CDN**: Automatically deployed to 300+ edge locations
- ⚡ **Instant Scaling**: Handle traffic spikes automatically
- 🔒 **Built-in Security**: DDoS protection and security headers
- 📊 **Analytics**: Real-time metrics and logs in Cloudflare Dashboard

## 🔧 Customization

### Adding New Features
1. Create new package in `packages/`
2. Add to `pnpm-workspace.yaml`
3. Export from main package entry point
4. Import in your applications

### Database Changes
```bash
# Make schema changes in packages/db/src/schema.ts
pnpm db:push          # Push to development
pnpm db:generate      # Generate migration
pnpm db:migrate       # Run migrations
```

### UI Components
```bash
# Add components to packages/ui-kit/src/components/
pnpm generate:component  # Use the generator
```

## 📚 Documentation

- [Remix Documentation](https://remix.run/docs)
- [Cloudflare Workers](https://developers.cloudflare.com/workers/)
- [Drizzle ORM](https://orm.drizzle.team/)
- [BetterAuth](https://better-auth.com/)
- [Radix UI](https://www.radix-ui.com/)
- [Tailwind CSS](https://tailwindcss.com/)

## 🤝 Contributing

This is a starter template. Feel free to:
- Fork and customize for your needs
- Submit issues and feature requests
- Create pull requests for improvements

## 📄 License

MIT License - see LICENSE file for details.
