/**
 * Error tracking and reporting utilities
 */

export interface ErrorContext {
  userId?: string;
  accountId?: string;
  requestId?: string;
  userAgent?: string;
  url?: string;
  ip?: string;
  environment?: string;
  version?: string;
  tags?: Record<string, string>;
  extra?: Record<string, unknown>;
}

export interface ErrorReport {
  id: string;
  timestamp: string;
  level: "error" | "warning" | "info";
  message: string;
  error: {
    name: string;
    message: string;
    stack?: string;
  };
  context: ErrorContext;
  fingerprint?: string;
}

export class ErrorTracker {
  private reports: ErrorReport[] = [];
  private globalContext: ErrorContext = {};

  constructor(
    private config: {
      environment: string;
      version?: string;
      maxReports?: number;
    }
  ) {
    this.globalContext.environment = config.environment;
    this.globalContext.version = config.version;
  }

  /**
   * Set global context for all error reports
   */
  setGlobalContext(context: Partial<ErrorContext>): void {
    this.globalContext = { ...this.globalContext, ...context };
  }

  /**
   * Report an error
   */
  reportError(
    error: Error,
    level: "error" | "warning" | "info" = "error",
    context: Partial<ErrorContext> = {}
  ): ErrorReport {
    const report: ErrorReport = {
      id: crypto.randomUUID(),
      timestamp: new Date().toISOString(),
      level,
      message: error.message,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      context: { ...this.globalContext, ...context },
      fingerprint: this.generateFingerprint(error),
    };

    this.addReport(report);
    this.logReport(report);

    return report;
  }

  /**
   * Report an exception with additional context
   */
  reportException(error: Error, context: Partial<ErrorContext> = {}): ErrorReport {
    return this.reportError(error, "error", context);
  }

  /**
   * Report a warning
   */
  reportWarning(message: string, context: Partial<ErrorContext> = {}): ErrorReport {
    const error = new Error(message);
    error.name = "Warning";
    return this.reportError(error, "warning", context);
  }

  /**
   * Report an info message
   */
  reportInfo(message: string, context: Partial<ErrorContext> = {}): ErrorReport {
    const error = new Error(message);
    error.name = "Info";
    return this.reportError(error, "info", context);
  }

  /**
   * Capture unhandled errors
   */
  captureUnhandledError(error: Error, context: Partial<ErrorContext> = {}): void {
    this.reportError(error, "error", {
      ...context,
      tags: { ...context.tags, unhandled: "true" },
    });
  }

  /**
   * Generate a fingerprint for error grouping
   */
  private generateFingerprint(error: Error): string {
    const key = `${error.name}:${error.message}:${this.getStackTrace(error)}`;
    return this.hashString(key);
  }

  private getStackTrace(error: Error): string {
    if (!error.stack) return "";

    // Get the first few lines of the stack trace for fingerprinting
    const lines = error.stack.split("\n").slice(0, 3);
    return lines.join("\n");
  }

  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  private addReport(report: ErrorReport): void {
    this.reports.push(report);

    // Limit stored reports to prevent memory issues
    const maxReports = this.config.maxReports || 100;
    if (this.reports.length > maxReports) {
      this.reports = this.reports.slice(-maxReports);
    }
  }

  private logReport(report: ErrorReport): void {
    // Log to console in structured format
    console.error(
      JSON.stringify({
        type: "error_report",
        ...report,
      })
    );
  }

  /**
   * Get all error reports
   */
  getReports(): ErrorReport[] {
    return [...this.reports];
  }

  /**
   * Get reports by level
   */
  getReportsByLevel(level: "error" | "warning" | "info"): ErrorReport[] {
    return this.reports.filter((report) => report.level === level);
  }

  /**
   * Get reports by fingerprint (grouped errors)
   */
  getReportsByFingerprint(fingerprint: string): ErrorReport[] {
    return this.reports.filter((report) => report.fingerprint === fingerprint);
  }

  /**
   * Clear all reports
   */
  clearReports(): void {
    this.reports = [];
  }

  /**
   * Get error statistics
   */
  getStatistics(): {
    total: number;
    byLevel: Record<string, number>;
    byFingerprint: Record<string, number>;
    recent: ErrorReport[];
  } {
    const byLevel: Record<string, number> = {};
    const byFingerprint: Record<string, number> = {};

    for (const report of this.reports) {
      byLevel[report.level] = (byLevel[report.level] || 0) + 1;
      if (report.fingerprint) {
        byFingerprint[report.fingerprint] = (byFingerprint[report.fingerprint] || 0) + 1;
      }
    }

    // Get recent reports (last 10)
    const recent = this.reports.slice(-10);

    return {
      total: this.reports.length,
      byLevel,
      byFingerprint,
      recent,
    };
  }
}

/**
 * Specialized error types for different scenarios
 */
export class ValidationError extends Error {
  constructor(
    message: string,
    public field?: string,
    public value?: unknown
  ) {
    super(message);
    this.name = "ValidationError";
  }
}

export class AuthenticationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "AuthenticationError";
  }
}

export class AuthorizationError extends Error {
  constructor(
    message: string,
    public required?: string
  ) {
    super(message);
    this.name = "AuthorizationError";
  }
}

export class RateLimitError extends Error {
  constructor(
    message: string,
    public limit?: number,
    public resetTime?: number
  ) {
    super(message);
    this.name = "RateLimitError";
  }
}

export class ExternalServiceError extends Error {
  constructor(
    message: string,
    public service?: string,
    public statusCode?: number
  ) {
    super(message);
    this.name = "ExternalServiceError";
  }
}

export class DatabaseError extends Error {
  constructor(
    message: string,
    public operation?: string,
    public table?: string
  ) {
    super(message);
    this.name = "DatabaseError";
  }
}

/**
 * Global error tracker instance
 */
let globalErrorTracker: ErrorTracker | undefined;

export function getErrorTracker(config?: {
  environment: string;
  version?: string;
  maxReports?: number;
}): ErrorTracker {
  if (!globalErrorTracker && config) {
    globalErrorTracker = new ErrorTracker(config);
  }
  if (!globalErrorTracker) {
    throw new Error("Error tracker not initialized. Call with config first.");
  }
  return globalErrorTracker;
}

/**
 * Error handling middleware
 */
export function createErrorHandlingMiddleware(errorTracker?: ErrorTracker) {
  return async (request: Request, handler: () => Promise<Response>): Promise<Response> => {
    try {
      return await handler();
    } catch (error) {
      const tracker = errorTracker || globalErrorTracker;

      if (tracker) {
        const url = new URL(request.url);
        tracker.reportException(error as Error, {
          url: request.url,
          userAgent: request.headers.get("user-agent") || undefined,
          ip:
            request.headers.get("cf-connecting-ip") ||
            request.headers.get("x-forwarded-for") ||
            undefined,
          extra: {
            method: request.method,
            pathname: url.pathname,
            search: url.search,
          },
        });
      }

      // Return appropriate error response based on error type
      if (error instanceof ValidationError) {
        return new Response(
          JSON.stringify({
            error: "Validation Error",
            message: error.message,
            field: error.field,
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      if (error instanceof AuthenticationError) {
        return new Response(
          JSON.stringify({
            error: "Authentication Error",
            message: error.message,
          }),
          {
            status: 401,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      if (error instanceof AuthorizationError) {
        return new Response(
          JSON.stringify({
            error: "Authorization Error",
            message: error.message,
          }),
          {
            status: 403,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      if (error instanceof RateLimitError) {
        const response = new Response(
          JSON.stringify({
            error: "Rate Limit Exceeded",
            message: error.message,
          }),
          {
            status: 429,
            headers: { "Content-Type": "application/json" },
          }
        );

        if (error.resetTime) {
          response.headers.set(
            "Retry-After",
            Math.ceil((error.resetTime - Date.now()) / 1000).toString()
          );
        }

        return response;
      }

      // Generic server error
      return new Response(
        JSON.stringify({
          error: "Internal Server Error",
          message: "An unexpected error occurred",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  };
}

/**
 * Utility to wrap async functions with error tracking
 */
export function withErrorTracking<T extends unknown[], R>(
  fn: (...args: T) => Promise<R>,
  context?: Partial<ErrorContext>
) {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args);
    } catch (error) {
      if (globalErrorTracker) {
        globalErrorTracker.reportException(error as Error, context);
      }
      throw error;
    }
  };
}
