-- Enable Row Level Security on all tables
ALTER TABLE accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE account_memberships ENABLE ROW LEVEL SECURITY;
ALTER TABLE sessions ENABLE ROW LEVEL SECURITY;

-- Helper function to get current user's primary account ID
CREATE OR REPLACE FUNCTION auth.get_current_user_id()
RETURNS uuid
LANGUAGE sql
STABLE
AS $$
  -- This would be set by your authentication middleware
  -- For now, using a simple session-based approach
  SELECT current_setting('app.current_user_id', true)::uuid;
$$;

-- Helper function to check if user has permission on account
CREATE OR REPLACE FUNCTION auth.has_permission_on_account(
  account_id uuid,
  permission_name text
)
RETURNS boolean
LANGUAGE sql
STABLE
AS $$
  SELECT EXISTS (
    SELECT 1 
    FROM account_memberships am
    WHERE am.account_id = $1
      AND am.user_id = auth.get_current_user_id()
      AND (
        -- Owners and admins have all permissions
        am.role IN ('owner', 'admin')
        OR 
        -- Check specific permissions
        $2 = ANY(am.permissions)
      )
  );
$$;

-- Helper function to check if user has role on account
CREATE OR REPLACE FUNCTION auth.has_role_on_account(
  account_id uuid,
  role_name text DEFAULT NULL
)
RETURNS boolean
LANGUAGE sql
STABLE
AS $$
  SELECT EXISTS (
    SELECT 1 
    FROM account_memberships am
    WHERE am.account_id = $1
      AND am.user_id = auth.get_current_user_id()
      AND (
        CASE 
          WHEN $2 IS NULL THEN TRUE
          ELSE am.role = $2
        END
      )
  );
$$;

-- RLS Policies for accounts table
CREATE POLICY select_accounts ON accounts
  FOR SELECT TO authenticated
  USING (auth.has_role_on_account(id));

CREATE POLICY insert_accounts ON accounts
  FOR INSERT TO authenticated
  WITH CHECK (auth.get_current_user_id() IS NOT NULL);

CREATE POLICY update_accounts ON accounts
  FOR UPDATE TO authenticated
  USING (auth.has_permission_on_account(id, 'accounts.write'))
  WITH CHECK (auth.has_permission_on_account(id, 'accounts.write'));

CREATE POLICY delete_accounts ON accounts
  FOR DELETE TO authenticated
  USING (auth.has_permission_on_account(id, 'accounts.delete'));

-- RLS Policies for users table
CREATE POLICY select_users ON users
  FOR SELECT TO authenticated
  USING (
    id = auth.get_current_user_id()
    OR EXISTS (
      SELECT 1 FROM account_memberships am
      WHERE am.user_id = users.id
        AND auth.has_role_on_account(am.account_id)
    )
  );

CREATE POLICY update_users ON users
  FOR UPDATE TO authenticated
  USING (
    id = auth.get_current_user_id()
    OR EXISTS (
      SELECT 1 FROM account_memberships am
      WHERE am.user_id = users.id
        AND auth.has_permission_on_account(am.account_id, 'users.write')
    )
  );

-- RLS Policies for account_memberships table
CREATE POLICY select_account_memberships ON account_memberships
  FOR SELECT TO authenticated
  USING (
    user_id = auth.get_current_user_id()
    OR auth.has_permission_on_account(account_id, 'users.read')
  );

CREATE POLICY insert_account_memberships ON account_memberships
  FOR INSERT TO authenticated
  WITH CHECK (auth.has_permission_on_account(account_id, 'users.write'));

CREATE POLICY update_account_memberships ON account_memberships
  FOR UPDATE TO authenticated
  USING (auth.has_permission_on_account(account_id, 'users.write'))
  WITH CHECK (auth.has_permission_on_account(account_id, 'users.write'));

CREATE POLICY delete_account_memberships ON account_memberships
  FOR DELETE TO authenticated
  USING (
    user_id = auth.get_current_user_id()
    OR auth.has_permission_on_account(account_id, 'users.delete')
  );

-- RLS Policies for sessions table
CREATE POLICY select_sessions ON sessions
  FOR SELECT TO authenticated
  USING (user_id = auth.get_current_user_id());

CREATE POLICY insert_sessions ON sessions
  FOR INSERT TO authenticated
  WITH CHECK (user_id = auth.get_current_user_id());

CREATE POLICY update_sessions ON sessions
  FOR UPDATE TO authenticated
  USING (user_id = auth.get_current_user_id())
  WITH CHECK (user_id = auth.get_current_user_id());

CREATE POLICY delete_sessions ON sessions
  FOR DELETE TO authenticated
  USING (user_id = auth.get_current_user_id());