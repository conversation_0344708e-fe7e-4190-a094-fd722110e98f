import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/cloudflare";
import { redirect } from "@remix-run/cloudflare";
import { getUser } from "~/lib/auth.server";

export const meta: MetaFunction = () => {
  return [
    { title: "Remix + Cloudflare Edge App" },
    { name: "description", content: "Welcome to your Remix app running on Cloudflare!" },
  ];
};

export async function loader({ request, context }: LoaderFunctionArgs) {
  const user = await getUser(request, context);

  if (user) {
    return redirect("/dashboard");
  }

  return redirect("/login");
}
