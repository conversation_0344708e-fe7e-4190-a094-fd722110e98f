{"$schema": "https://biomejs.dev/schemas/2.0.6/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "includes": ["**", "!**/node_modules/**", "!**/dist/**", "!**/.next/**", "!**/coverage/**", "!**/.turbo/**"]}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noNonNullAssertion": "off", "noParameterAssign": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "noUselessElse": "error", "noRestrictedImports": {"level": "error", "options": {"paths": {"packages/infra": "Use specific infra imports instead of root package", "packages/ui-kit": "Use specific ui-kit component imports instead of root package"}}}}, "correctness": {"noUnusedVariables": "error", "noUnusedImports": "error"}, "nursery": {}}}, "javascript": {"formatter": {"quoteStyle": "double", "trailingCommas": "es5", "semicolons": "always"}}, "json": {"formatter": {"trailingCommas": "none"}}}