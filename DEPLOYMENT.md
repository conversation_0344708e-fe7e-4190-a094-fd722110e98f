# 🚀 Deployment Guide

本项目采用单一 Cloudflare Worker 架构，提供简化的部署流程和卓越的全球性能。

## 📋 部署前准备

### 1. 安装 Wrangler CLI
```bash
npm install -g wrangler
# 或者使用项目中的版本
pnpm exec wrangler --version
```

### 2. 登录 Cloudflare
```bash
wrangler auth login
```

### 3. 配置环境变量
确保在 Cloudflare 控制台中设置了必要的环境变量和秘钥。

## 🛠️ 部署命令

### 一键部署应用
```bash
pnpm run deploy
```
这个命令会：
1. 构建 Remix 应用
2. 部署到 Cloudflare Workers
3. 配置静态资源服务

### 手动部署步骤

#### 1. 构建应用
```bash
pnpm run build
```

#### 2. 部署到 Cloudflare Workers
```bash
cd apps/web
pnpm run deploy
```

### 环境变量配置
```bash
# 设置数据库连接
wrangler secret put DATABASE_URL

# 设置认证密钥
wrangler secret put AUTH_SECRET

# 设置 Stripe 密钥
wrangler secret put STRIPE_SECRET_KEY

# 设置其他必要的环境变量
wrangler secret put OTHER_SECRET_KEY
```

## 📦 部署架构

```
┌─────────────────────────────────────────┐
│         Cloudflare Worker               │
│                                         │
│   Single Unified Application            │
│   ├── Remix SSR Engine                  │
│   ├── Static Assets Serving             │
│   ├── API Routes (/api/*)               │
│   ├── Authentication (BetterAuth)       │
│   ├── Database Integration (Neon)       │
│   ├── File Storage (R2)                 │
│   └── AI Integration                    │
│                                         │
│   remix-cloudflare-neon-starter         │
│   🌍 部署至全球 300+ 边缘节点              │
└─────────────────────────────────────────┘
```

### 架构优势
- ⚡ **零冷启动**: V8 隔离技术实现毫秒级响应
- 🌍 **全球部署**: 自动部署到全球边缘网络
- 💰 **成本优化**: 按使用量计费，无闲置成本
- 🚀 **自动扩展**: 无缝处理流量峰值
- 🔒 **内置安全**: DDoS 防护和安全头部

## 🔧 配置文件

### Worker 应用 (apps/web/wrangler.toml)
- **部署类型**: Cloudflare Workers
- **入口文件**: `./worker.js`
- **静态资源**: `./build/client` (通过 assets 配置)
- **项目名称**: `remix-cloudflare-neon-starter`
- **兼容日期**: `2025-06-26`
- **绑定**: AI、R2存储、KV 缓存等

### 关键配置项
```toml
# 基础配置
name = "remix-cloudflare-neon-starter"
main = "./worker.js"
compatibility_date = "2025-06-26"

# 静态资源配置
[assets]
directory = "./build/client"
binding = "ASSETS"

# 环境变量
[vars]
NODE_ENV = "production"

# R2 存储绑定
[[r2_buckets]]
binding = "R2_BUCKET"
bucket_name = "your-r2-bucket"

# AI 绑定
[ai]
binding = "AI"
```

## 🌐 访问地址

部署完成后，你会获得一个主要地址：

**应用地址**: `https://remix-cloudflare-neon-starter.workers.dev`

### 自定义域名配置
```bash
# 在 Cloudflare 控制台中配置自定义域名
# 或者在 wrangler.toml 中添加路由配置
```

## 📝 部署日志

### 成功示例
```bash
$ pnpm run deploy

> web@0.1.0 deploy
> wrangler deploy

 ⛅️ wrangler 3.114.10
-------------------
✓ Built successfully
✓ Uploading... (100%)

✨ Successfully published your Worker
   https://remix-cloudflare-neon-starter.workers.dev
   
📊 View metrics and logs:
   https://dash.cloudflare.com/workers/analytics
```

## 🔍 故障排除

### 常见问题

1. **认证失败**
   ```bash
   wrangler auth login
   ```

2. **构建失败**
   ```bash
   pnpm run build
   ```

3. **环境变量缺失**
   - 检查 wrangler.toml 配置
   - 在 Cloudflare 控制台中设置秘钥
   ```bash
   wrangler secret put SECRET_NAME
   ```

4. **域名配置**
   - Workers: 在 wrangler.toml 中配置路由
   - 或在 Cloudflare 控制台中设置自定义域名

## 🚨 生产环境注意事项

1. **环境变量**: 确保生产环境的秘钥已正确设置
2. **域名配置**: 配置自定义域名和 SSL 证书
3. **缓存策略**: 检查 CDN 缓存配置
4. **监控**: 设置 Cloudflare Analytics 和错误监控
5. **资源限制**: 监控 Worker 的 CPU 和内存使用
6. **数据库连接**: 确保 Neon 数据库连接池配置正确
