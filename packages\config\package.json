{"name": "@repo/config", "version": "0.1.0", "type": "module", "private": true, "exports": {".": "./dist/index.js", "./env": "./dist/env/index.js"}, "types": "./dist/index.d.ts", "scripts": {"dev": "tsc --watch", "build": "tsc", "check-types": "tsc --noEmit", "lint": "biome check .", "lint:fix": "biome check --write ."}, "dependencies": {"zod": "^3.24.1", "@repo/shared": "workspace:*"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/node": "^22.15.3", "typescript": "5.8.2"}}