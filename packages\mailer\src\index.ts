import type { Env } from "@repo/config";
import { Resend } from "resend";

export interface EmailOptions {
  to: string | string[];
  subject: string;
  html?: string;
  text?: string;
  from?: string;
}

export class MailService {
  private resend: Resend;
  private defaultFrom: string;

  constructor(apiKey: string, defaultFrom = "<EMAIL>") {
    this.resend = new Resend(apiKey);
    this.defaultFrom = defaultFrom;
  }

  async sendEmail(options: EmailOptions) {
    try {
      const emailData = {
        from: options.from || this.defaultFrom,
        to: Array.isArray(options.to) ? options.to : [options.to],
        subject: options.subject,
        ...(options.html ? { html: options.html } : {}),
        ...(options.text ? { text: options.text } : { text: options.subject }),
      };

      const result = await this.resend.emails.send(emailData);

      return { success: true, data: result };
    } catch (error) {
      console.error("Email send error:", error);
      return { success: false, error: error instanceof Error ? error.message : "Unknown error" };
    }
  }

  async sendWelcomeEmail(to: string, name: string) {
    return this.sendEmail({
      to,
      subject: "Welcome to our platform!",
      html: `<h1>Welcome ${name}!</h1><p>Thanks for joining us.</p>`,
      text: `Welcome ${name}! Thanks for joining us.`,
    });
  }
}

export function createMailService(env: Pick<Env, "RESEND_API_KEY">) {
  if (!env.RESEND_API_KEY) {
    throw new Error("RESEND_API_KEY is required");
  }

  return new MailService(env.RESEND_API_KEY);
}
