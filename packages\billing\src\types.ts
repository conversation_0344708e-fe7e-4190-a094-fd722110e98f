import { z } from "zod";

/**
 * Common billing types and schemas
 */

// Subscription status enum
export const subscriptionStatusSchema = z.enum([
  "active",
  "canceled",
  "incomplete",
  "incomplete_expired",
  "past_due",
  "trialing",
  "unpaid",
  "paused",
]);

export type SubscriptionStatus = z.infer<typeof subscriptionStatusSchema>;

// Product and pricing schemas
export const productSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  active: z.boolean(),
  metadata: z.record(z.string()).optional(),
});

export const priceSchema = z.object({
  id: z.string(),
  productId: z.string(),
  unitAmount: z.number(),
  currency: z.string(),
  interval: z.enum(["day", "week", "month", "year"]).optional(),
  intervalCount: z.number().optional(),
  type: z.enum(["one_time", "recurring"]),
  active: z.boolean(),
  metadata: z.record(z.string()).optional(),
});

export type Product = z.infer<typeof productSchema>;
export type Price = z.infer<typeof priceSchema>;

// Customer schemas
export const customerSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  name: z.string().optional(),
  metadata: z.record(z.string()).optional(),
});

export type Customer = z.infer<typeof customerSchema>;

// Subscription schemas
export const subscriptionSchema = z.object({
  id: z.string(),
  customerId: z.string(),
  status: subscriptionStatusSchema,
  currentPeriodStart: z.date(),
  currentPeriodEnd: z.date(),
  cancelAtPeriodEnd: z.boolean(),
  canceledAt: z.date().optional(),
  trialStart: z.date().optional(),
  trialEnd: z.date().optional(),
  metadata: z.record(z.string()).optional(),
  items: z.array(
    z.object({
      id: z.string(),
      priceId: z.string(),
      quantity: z.number(),
    })
  ),
});

export type Subscription = z.infer<typeof subscriptionSchema>;

// Invoice schemas
export const invoiceSchema = z.object({
  id: z.string(),
  customerId: z.string(),
  subscriptionId: z.string().optional(),
  status: z.enum(["draft", "open", "paid", "uncollectible", "void"]),
  amount: z.number(),
  currency: z.string(),
  created: z.date(),
  dueDate: z.date().optional(),
  paidAt: z.date().optional(),
  metadata: z.record(z.string()).optional(),
});

export type Invoice = z.infer<typeof invoiceSchema>;

// Checkout session schemas
export const checkoutSessionSchema = z.object({
  id: z.string(),
  url: z.string(),
  customerId: z.string().optional(),
  mode: z.enum(["payment", "subscription", "setup"]),
  status: z.enum(["open", "complete", "expired"]),
  successUrl: z.string(),
  cancelUrl: z.string(),
  metadata: z.record(z.string()).optional(),
});

export type CheckoutSession = z.infer<typeof checkoutSessionSchema>;

// Webhook event schemas
export const webhookEventSchema = z.object({
  id: z.string(),
  type: z.string(),
  data: z.unknown(),
  created: z.date(),
});

export type WebhookEvent = z.infer<typeof webhookEventSchema>;

// Common billing provider interface
export interface BillingProvider {
  // Products and pricing
  getProducts(): Promise<Product[]>;
  getProduct(productId: string): Promise<Product | null>;
  getPrices(productId?: string): Promise<Price[]>;
  getPrice(priceId: string): Promise<Price | null>;

  // Customers
  createCustomer(data: {
    email: string;
    name?: string;
    metadata?: Record<string, string>;
  }): Promise<Customer>;
  getCustomer(customerId: string): Promise<Customer | null>;
  updateCustomer(customerId: string, data: Partial<Customer>): Promise<Customer>;
  deleteCustomer(customerId: string): Promise<void>;

  // Subscriptions
  createSubscription(data: {
    customerId: string;
    priceId: string;
    quantity?: number;
    metadata?: Record<string, string>;
    trialPeriodDays?: number;
  }): Promise<Subscription>;
  getSubscription(subscriptionId: string): Promise<Subscription | null>;
  updateSubscription(
    subscriptionId: string,
    data: {
      priceId?: string;
      quantity?: number;
      metadata?: Record<string, string>;
    }
  ): Promise<Subscription>;
  cancelSubscription(subscriptionId: string, cancelAtPeriodEnd?: boolean): Promise<Subscription>;

  // Invoices
  getInvoices(customerId: string): Promise<Invoice[]>;
  getInvoice(invoiceId: string): Promise<Invoice | null>;

  // Checkout
  createCheckoutSession(data: {
    mode: "payment" | "subscription" | "setup";
    priceId?: string;
    customerId?: string;
    successUrl: string;
    cancelUrl: string;
    metadata?: Record<string, string>;
  }): Promise<CheckoutSession>;

  // Customer portal
  createCustomerPortalSession(customerId: string, returnUrl: string): Promise<{ url: string }>;

  // Webhooks
  constructWebhookEvent(payload: string, signature: string, secret: string): Promise<WebhookEvent>;

  // Usage-based billing
  reportUsage?(subscriptionItemId: string, quantity: number, timestamp?: Date): Promise<void>;
}
