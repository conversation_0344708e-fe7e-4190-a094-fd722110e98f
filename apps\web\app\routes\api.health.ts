import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";

export async function loader({ context }: LoaderFunctionArgs) {
  const env = context.cloudflare.env;

  return json({
    status: "healthy",
    environment: env.NODE_ENV || "development",
    services: {
      r2: "connected",
      kv: "connected",
      ai: "connected",
    },
    timestamp: new Date().toISOString(),
  });
}
