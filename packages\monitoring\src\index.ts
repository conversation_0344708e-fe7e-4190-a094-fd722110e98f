export * from "./errors.js";
export * from "./logger.js";
export * from "./metrics.js";

import { ErrorTracker } from "./errors.js";
// Import classes for local use
import { Logger } from "./logger.js";
import { BusinessMetrics, MetricsCollector, PerformanceMonitor } from "./metrics.js";

export {
  AuthenticationError,
  AuthorizationError,
  createErrorHandlingMiddleware,
  DatabaseError,
  ErrorTracker,
  ExternalServiceError,
  getErrorTracker,
  RateLimitError,
  ValidationError,
  withErrorTracking,
} from "./errors.js";
// Re-export commonly used classes and functions
export {
  createLogger,
  createLoggingMiddleware,
  Logger,
} from "./logger.js";
export {
  BusinessMetrics,
  createMetricsMiddleware,
  getBusinessMetrics,
  getMetricsCollector,
  getPerformanceMonitor,
  MetricsCollector,
  PerformanceMonitor,
} from "./metrics.js";

// Convenience function to initialize all monitoring
export function initializeMonitoring(config: {
  service: string;
  environment: string;
  version?: string;
  logLevel?: "debug" | "info" | "warn" | "error" | "fatal";
}) {
  const logger = Logger.getInstance({
    level: config.logLevel || "info",
    service: config.service,
    environment: config.environment,
    version: config.version,
  });

  const errorTracker = new ErrorTracker({
    environment: config.environment,
    version: config.version,
  });

  return {
    logger,
    errorTracker,
    metricsCollector: new MetricsCollector(),
    businessMetrics: new BusinessMetrics(new MetricsCollector()),
    performanceMonitor: new PerformanceMonitor(new MetricsCollector()),
  };
}
